import React, { useState, useEffect } from 'react';
import ReactECharts from 'echarts-for-react';
import {
  <PERSON><PERSON><PERSON><PERSON>,
  ChartHeader,
  ChartTitle,
  Disclaimer,
  LoadingSpinner
} from '../styles/StyledComponents';
import { ChartData } from '../types';
import {
  loadPredictionCSV,
  filterPredictionsByDateRange,
  PredictionData,
  DateRange
} from '../utils/csvDataLoader';
import DateRangePicker from './DateRangePicker';

const BitcoinPriceChart: React.FC = () => {
  const [chartData, setChartData] = useState<ChartData | null>(null);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [predictionData, setPredictionData] = useState<PredictionData[]>([]);
  const [currentDateRange, setCurrentDateRange] = useState<DateRange | null>(null);

  // Load CSV data on component mount
  useEffect(() => {
    const loadCSVData = async () => {
      try {
        setLoading(true);
        setError(null);

        console.log('Loading prediction data from CSV...');
        const data = await loadPredictionCSV();
        setPredictionData(data);

        // Set default date range (1 month from tomorrow)
        const tomorrow = new Date();
        tomorrow.setDate(tomorrow.getDate() + 1);
        const defaultEndDate = new Date(tomorrow);
        defaultEndDate.setMonth(defaultEndDate.getMonth() + 1);

        const defaultRange: DateRange = {
          startDate: tomorrow,
          endDate: defaultEndDate
        };

        setCurrentDateRange(defaultRange);

        // Filter data for default range
        const filteredData = filterPredictionsByDateRange(data, defaultRange);
        setChartData({
          dates: filteredData.dates,
          prices: filteredData.prices
        });

        console.log(`Successfully loaded ${data.length} prediction records from CSV`);
      } catch (err: any) {
        console.error('Error loading CSV data:', err);
        setError(err.message || 'Failed to load prediction data from CSV. Please ensure the model has been trained and CSV file exists.');
      } finally {
        setLoading(false);
      }
    };

    loadCSVData();
  }, []);

  // Handle date range changes
  const handleDateRangeChange = (dateRange: DateRange) => {
    try {
      setCurrentDateRange(dateRange);

      const filteredData = filterPredictionsByDateRange(predictionData, dateRange);

      if (filteredData.dates.length === 0) {
        setError('No prediction data available for the selected date range.');
        setChartData(null);
      } else {
        setError(null);
        setChartData({
          dates: filteredData.dates,
          prices: filteredData.prices
        });
        console.log(`Filtered data for date range: ${filteredData.dates.length} records`);
      }
    } catch (err: any) {
      console.error('Error filtering data by date range:', err);
      setError('Error filtering data for the selected date range.');
    }
  };

  const getOption = () => {
    if (!chartData) return {};

    return {
      tooltip: {
        trigger: 'axis',
        backgroundColor: 'rgba(13, 17, 23, 0.9)',
        borderColor: '#30363d',
        textStyle: {
          color: '#e6e8ea',
        },
        formatter: (params: any) => {
          const dataIndex = params[0].dataIndex;
          const date = chartData.dates[dataIndex];
          const price = chartData.prices[dataIndex].toLocaleString('en-US', {
            style: 'currency',
            currency: 'USD',
          });
          return `<strong>${date}</strong><br/>Predicted Price: ${price}`;
        },
      },
      grid: {
        left: '3%',
        right: '4%',
        bottom: '3%',
        containLabel: true,
      },
      xAxis: {
        type: 'category',
        data: chartData.dates,
        axisLine: {
          lineStyle: {
            color: '#30363d',
          },
        },
        axisLabel: {
          color: '#8b949e',
          formatter: (value: string) => {
            const date = new Date(value);
            return date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' });
          },
        },
        boundaryGap: false,
      },
      yAxis: {
        type: 'value',
        axisLine: {
          lineStyle: {
            color: '#30363d',
          },
        },
        splitLine: {
          lineStyle: {
            color: '#21262d',
          },
        },
        axisLabel: {
          color: '#8b949e',
          formatter: (value: number) => {
            return '$' + value.toLocaleString();
          },
        },
        scale: true,
        min: (value: { min: number }) => {
          // Set min to ~20% below the minimum value
          return Math.floor(value.min * 0.8);
        },
        max: (value: { max: number }) => {
          // Set max to ~20% above the maximum value
          return Math.ceil(value.max * 1.2);
        },
        splitNumber: 20,
        minInterval: 500
      },
      series: [
        {
          name: 'BTC Price Prediction',
          type: 'line',
          data: chartData.prices,
          smooth: true,
          symbol: 'none',
          lineStyle: {
            color: '#f7931a',
            width: 3,
          },
          areaStyle: {
            color: {
              type: 'linear',
              x: 0,
              y: 0,
              x2: 0,
              y2: 1,
              colorStops: [
                {
                  offset: 0,
                  color: 'rgba(247, 147, 26, 0.5)',
                },
                {
                  offset: 1,
                  color: 'rgba(247, 147, 26, 0.05)',
                },
              ],
            },
          },
        },
      ],
    };
  };

  const renderErrorMessage = () => {
    const isCSVError = error?.includes('CSV') || error?.includes('model has been trained');

    return (
      <div style={{
        height: '400px',
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        color: '#f85149',
        textAlign: 'center',
        padding: '0 20px'
      }}>
        <div style={{ fontSize: '1.2rem', marginBottom: '10px' }}>
          {error}
        </div>

        {isCSVError && (
          <div style={{
            color: '#8b949e',
            fontSize: '0.9rem',
            marginTop: '20px',
            maxWidth: '600px',
            textAlign: 'left',
            background: 'rgba(255,255,255,0.05)',
            padding: '15px',
            borderRadius: '5px'
          }}>
            <div style={{ fontWeight: 'bold', marginBottom: '8px' }}>To fix this issue:</div>
            <ol style={{ paddingLeft: '20px', margin: 0 }}>
              <li>Run the AI model training script to generate predictions</li>
              <li>Navigate to the AI directory: <code style={{ background: '#21262d', padding: '3px 5px', borderRadius: '3px' }}>cd AI</code></li>
              <li>Run: <code style={{ background: '#21262d', padding: '3px 5px', borderRadius: '3px' }}>python bitcoin_price_prediction_using_lstm.py</code></li>
              <li>Wait for training to complete and CSV file to be generated</li>
              <li>Refresh this page to load the new predictions</li>
            </ol>
          </div>
        )}

        <button
          onClick={() => window.location.reload()}
          style={{
            marginTop: '15px',
            padding: '8px 15px',
            backgroundColor: 'var(--accent)',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          Retry
        </button>
      </div>
    );
  };

  return (
    <ChartContainer>
      <ChartHeader>
        <ChartTitle>Bitcoin Price Prediction</ChartTitle>
      </ChartHeader>

      {!loading && !error && predictionData.length > 0 && (
        <DateRangePicker
          onDateRangeChange={handleDateRangeChange}
          initialStartDate={currentDateRange?.startDate}
          initialEndDate={currentDateRange?.endDate}
        />
      )}

      {loading ? (
        <LoadingSpinner />
      ) : error ? (
        renderErrorMessage()
      ) : (
        <ReactECharts
          option={getOption()}
          style={{ height: '600px' }}
          opts={{ renderer: 'canvas' }}
          notMerge={true}
          lazyUpdate={true}
        />
      )}
      <Disclaimer>
        * This is a prediction model and should not be used as financial advice.
      </Disclaimer>
    </ChartContainer>
  );
};

export default BitcoinPriceChart; 