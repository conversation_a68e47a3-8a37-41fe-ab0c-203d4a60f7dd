import React, { useState, useEffect } from 'react';
import styled from 'styled-components';
import { DateRange, validateDateRange } from '../utils/csvDataLoader';

const DatePickerContainer = styled.div`
  display: flex;
  flex-direction: column;
  gap: 15px;
  padding: 20px;
  background: var(--card-bg);
  border: 1px solid var(--border);
  border-radius: 8px;
  margin-bottom: 20px;
`;

const DatePickerHeader = styled.h3`
  margin: 0 0 10px 0;
  color: var(--text-primary);
  font-size: 1.1rem;
  font-weight: 600;
`;

const DateInputRow = styled.div`
  display: flex;
  gap: 15px;
  align-items: center;
  flex-wrap: wrap;
`;

const DateInputGroup = styled.div`
  display: flex;
  flex-direction: column;
  gap: 5px;
`;

const DateLabel = styled.label`
  color: var(--text-secondary);
  font-size: 0.9rem;
  font-weight: 500;
`;

const DateInput = styled.input`
  padding: 8px 12px;
  border: 1px solid var(--border);
  border-radius: 4px;
  background: var(--input-bg);
  color: var(--text-primary);
  font-size: 0.9rem;
  min-width: 140px;
  
  &:focus {
    outline: none;
    border-color: var(--accent);
    box-shadow: 0 0 0 2px rgba(247, 147, 26, 0.2);
  }
  
  &:invalid {
    border-color: #f85149;
  }
`;

const ApplyButton = styled.button`
  padding: 8px 16px;
  background: var(--accent);
  color: white;
  border: none;
  border-radius: 4px;
  font-size: 0.9rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover:not(:disabled) {
    background: #e8851e;
    transform: translateY(-1px);
  }
  
  &:disabled {
    background: var(--text-secondary);
    cursor: not-allowed;
    opacity: 0.6;
  }
`;

const ErrorMessage = styled.div`
  color: #f85149;
  font-size: 0.85rem;
  margin-top: 5px;
  padding: 8px 12px;
  background: rgba(248, 81, 73, 0.1);
  border: 1px solid rgba(248, 81, 73, 0.3);
  border-radius: 4px;
`;

const InfoMessage = styled.div`
  color: var(--text-secondary);
  font-size: 0.8rem;
  margin-top: 5px;
  padding: 8px 12px;
  background: rgba(139, 148, 158, 0.1);
  border: 1px solid rgba(139, 148, 158, 0.3);
  border-radius: 4px;
`;

const QuickSelectButtons = styled.div`
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
`;

const QuickSelectButton = styled.button`
  padding: 6px 12px;
  background: transparent;
  color: var(--text-secondary);
  border: 1px solid var(--border);
  border-radius: 4px;
  font-size: 0.8rem;
  cursor: pointer;
  transition: all 0.2s ease;
  
  &:hover {
    background: var(--hover-bg);
    color: var(--text-primary);
    border-color: var(--accent);
  }
`;

interface DateRangePickerProps {
  onDateRangeChange: (dateRange: DateRange) => void;
  initialStartDate?: Date;
  initialEndDate?: Date;
}

const DateRangePicker: React.FC<DateRangePickerProps> = ({
  onDateRangeChange,
  initialStartDate,
  initialEndDate
}) => {
  const [startDate, setStartDate] = useState<string>('');
  const [endDate, setEndDate] = useState<string>('');
  const [error, setError] = useState<string>('');
  const [isValid, setIsValid] = useState<boolean>(false);

  // Set default dates on component mount
  useEffect(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const defaultEndDate = new Date(tomorrow);
    defaultEndDate.setMonth(defaultEndDate.getMonth() + 1); // 1 month from tomorrow
    
    const startDateStr = initialStartDate ? 
      initialStartDate.toISOString().split('T')[0] : 
      tomorrow.toISOString().split('T')[0];
    
    const endDateStr = initialEndDate ? 
      initialEndDate.toISOString().split('T')[0] : 
      defaultEndDate.toISOString().split('T')[0];
    
    setStartDate(startDateStr);
    setEndDate(endDateStr);
  }, [initialStartDate, initialEndDate]);

  // Validate dates whenever they change
  useEffect(() => {
    if (startDate && endDate) {
      const start = new Date(startDate);
      const end = new Date(endDate);
      
      const validation = validateDateRange(start, end);
      setIsValid(validation.isValid);
      setError(validation.errorMessage || '');
    } else {
      setIsValid(false);
      setError('Please select both start and end dates');
    }
  }, [startDate, endDate]);

  const handleApply = () => {
    if (isValid && startDate && endDate) {
      const dateRange: DateRange = {
        startDate: new Date(startDate),
        endDate: new Date(endDate)
      };
      onDateRangeChange(dateRange);
    }
  };

  const handleQuickSelect = (days: number) => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    
    const end = new Date(tomorrow);
    end.setDate(end.getDate() + days);
    
    setStartDate(tomorrow.toISOString().split('T')[0]);
    setEndDate(end.toISOString().split('T')[0]);
  };

  // Calculate min and max dates for input constraints
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const minDate = tomorrow.toISOString().split('T')[0];
  
  const maxDate = new Date();
  maxDate.setFullYear(maxDate.getFullYear() + 3);
  const maxDateStr = maxDate.toISOString().split('T')[0];

  return (
    <DatePickerContainer>
      <DatePickerHeader>Select Prediction Date Range</DatePickerHeader>
      
      <QuickSelectButtons>
        <QuickSelectButton onClick={() => handleQuickSelect(30)}>
          1 Month
        </QuickSelectButton>
        <QuickSelectButton onClick={() => handleQuickSelect(90)}>
          3 Months
        </QuickSelectButton>
        <QuickSelectButton onClick={() => handleQuickSelect(180)}>
          6 Months
        </QuickSelectButton>
        <QuickSelectButton onClick={() => handleQuickSelect(365)}>
          1 Year
        </QuickSelectButton>
        <QuickSelectButton onClick={() => handleQuickSelect(1095)}>
          3 Years
        </QuickSelectButton>
      </QuickSelectButtons>
      
      <DateInputRow>
        <DateInputGroup>
          <DateLabel htmlFor="start-date">Start Date</DateLabel>
          <DateInput
            id="start-date"
            type="date"
            value={startDate}
            min={minDate}
            max={maxDateStr}
            onChange={(e) => setStartDate(e.target.value)}
          />
        </DateInputGroup>
        
        <DateInputGroup>
          <DateLabel htmlFor="end-date">End Date</DateLabel>
          <DateInput
            id="end-date"
            type="date"
            value={endDate}
            min={minDate}
            max={maxDateStr}
            onChange={(e) => setEndDate(e.target.value)}
          />
        </DateInputGroup>
        
        <ApplyButton 
          onClick={handleApply} 
          disabled={!isValid}
        >
          Apply Range
        </ApplyButton>
      </DateInputRow>
      
      {error && <ErrorMessage>{error}</ErrorMessage>}
      
      {!error && startDate && endDate && (
        <InfoMessage>
          Selected range: {new Date(startDate).toLocaleDateString()} to {new Date(endDate).toLocaleDateString()}
          ({Math.ceil((new Date(endDate).getTime() - new Date(startDate).getTime()) / (1000 * 60 * 60 * 24))} days)
        </InfoMessage>
      )}
    </DatePickerContainer>
  );
};

export default DateRangePicker;
