import Papa from 'papaparse';

export interface PredictionData {
  date: string;
  predictedPrice: number;
}

export interface DateRange {
  startDate: Date;
  endDate: Date;
}

export interface FilteredPredictionData {
  dates: string[];
  prices: number[];
}

/**
 * Load and parse the CSV file containing Bitcoin price predictions
 * @returns Promise<PredictionData[]> Array of prediction data
 */
export const loadPredictionCSV = async (): Promise<PredictionData[]> => {
  try {
    // Load CSV file from public directory
    const response = await fetch('/Data/bitcoin_predictions_5year.csv');
    
    if (!response.ok) {
      throw new Error(`Failed to load CSV file: ${response.status} ${response.statusText}`);
    }
    
    const csvText = await response.text();
    
    return new Promise((resolve, reject) => {
      Papa.parse(csvText, {
        header: true,
        skipEmptyLines: true,
        complete: (results) => {
          try {
            const data: PredictionData[] = results.data.map((row: any) => ({
              date: row.Date,
              predictedPrice: parseFloat(row.Predicted_Price)
            }));
            
            // Validate data
            const validData = data.filter(item => 
              item.date && 
              !isNaN(item.predictedPrice) && 
              item.predictedPrice > 0
            );
            
            if (validData.length === 0) {
              throw new Error('No valid prediction data found in CSV');
            }
            
            console.log(`Loaded ${validData.length} prediction records from CSV`);
            resolve(validData);
          } catch (error) {
            reject(new Error(`Error parsing CSV data: ${error}`));
          }
        },
        error: (error) => {
          reject(new Error(`CSV parsing error: ${error.message}`));
        }
      });
    });
  } catch (error) {
    console.error('Error loading prediction CSV:', error);
    throw error;
  }
};

/**
 * Filter prediction data by date range
 * @param data Array of prediction data
 * @param dateRange Date range to filter by
 * @returns Filtered prediction data in chart format
 */
export const filterPredictionsByDateRange = (
  data: PredictionData[], 
  dateRange: DateRange
): FilteredPredictionData => {
  const startTime = dateRange.startDate.getTime();
  const endTime = dateRange.endDate.getTime();
  
  const filteredData = data.filter(item => {
    const itemDate = new Date(item.date);
    const itemTime = itemDate.getTime();
    return itemTime >= startTime && itemTime <= endTime;
  });
  
  return {
    dates: filteredData.map(item => item.date),
    prices: filteredData.map(item => item.predictedPrice)
  };
};

/**
 * Get prediction data for a specific number of days from today
 * @param data Array of prediction data
 * @param days Number of days from today
 * @returns Filtered prediction data in chart format
 */
export const getPredictionsByDays = (
  data: PredictionData[], 
  days: number
): FilteredPredictionData => {
  const today = new Date();
  const endDate = new Date(today);
  endDate.setDate(today.getDate() + days);
  
  return filterPredictionsByDateRange(data, {
    startDate: today,
    endDate: endDate
  });
};

/**
 * Get available date range from the prediction data
 * @param data Array of prediction data
 * @returns Object with min and max dates
 */
export const getAvailableDateRange = (data: PredictionData[]): { minDate: Date; maxDate: Date } => {
  if (data.length === 0) {
    const today = new Date();
    return { minDate: today, maxDate: today };
  }
  
  const dates = data.map(item => new Date(item.date));
  const minDate = new Date(Math.min(...dates.map(d => d.getTime())));
  const maxDate = new Date(Math.max(...dates.map(d => d.getTime())));
  
  return { minDate, maxDate };
};

/**
 * Validate if a date range is within allowed constraints
 * @param startDate Start date
 * @param endDate End date
 * @returns Object with validation result and error message if invalid
 */
export const validateDateRange = (
  startDate: Date, 
  endDate: Date
): { isValid: boolean; errorMessage?: string } => {
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);
  
  const maxAllowedDate = new Date(today);
  maxAllowedDate.setFullYear(today.getFullYear() + 3); // 3 years from today
  
  // Check if start date is at least tomorrow
  if (startDate < tomorrow) {
    return {
      isValid: false,
      errorMessage: 'Start date must be at least 1 day from today'
    };
  }
  
  // Check if end date is within 3 years
  if (endDate > maxAllowedDate) {
    return {
      isValid: false,
      errorMessage: 'End date cannot be more than 3 years from today'
    };
  }
  
  // Check if start date is before end date
  if (startDate >= endDate) {
    return {
      isValid: false,
      errorMessage: 'Start date must be before end date'
    };
  }
  
  // Check if date range is not too long (max 3 years)
  const daysDifference = Math.ceil((endDate.getTime() - startDate.getTime()) / (1000 * 60 * 60 * 24));
  if (daysDifference > 1095) { // 3 years = 1095 days
    return {
      isValid: false,
      errorMessage: 'Date range cannot exceed 3 years'
    };
  }
  
  return { isValid: true };
};
