# Bitcoin Price Prediction Project

A comprehensive application for predicting Bitcoin prices using LSTM (Long Short-Term Memory) neural networks with a professional dark-themed frontend visualization. This project combines machine learning with a responsive web interface to provide Bitcoin price predictions for various timeframes.

## Project Overview

This application consists of:
1. A backend LSTM model for Bitcoin price prediction
2. A Flask API to serve predictions
3. A React frontend with interactive charts for data visualization

## Project Structure

```
hackthon_hnt_team/
├── AI/
│   ├── bitcoin_price_prediction_using_lstm.py  # LSTM model training script
│   └── model/                                 # Trained LSTM model files
│       ├── bitcoin_lstm_model.keras
│       ├── bitcoin_price_scaler.save
│       ├── loss_curves.png
│       └── actual_vs_predicted.png
├── Data/
│   ├── Bitcoin Historical Data.csv            # Historical Bitcoin price data
│   └── bitcoin_predictions_5year.csv          # Generated 5-year predictions
├── Frontend/                                  # React frontend application
│   ├── public/
│   │   └── Data/
│   │       └── bitcoin_predictions_5year.csv  # CSV copy for frontend access
│   └── src/
│       ├── components/
│       │   ├── BitcoinPriceChart.tsx          # Main chart component
│       │   └── DateRangePicker.tsx            # Custom date picker
│       └── utils/
│           └── csvDataLoader.ts               # CSV data loading utilities
└── README.md                                  # This file
```

## Features

### AI Model
- **Advanced LSTM Architecture**: Multi-layer LSTM with GRU ensemble for enhanced accuracy
- **5-Year Predictions**: Generates comprehensive daily predictions for 5 years ahead
- **Rolling Window Validation**: Robust model evaluation using time series validation
- **Multivariate Analysis**: Incorporates both price and volume data for better predictions
- **Enhanced Training Strategy**: Combines multiple model architectures (LSTM, GRU, Advanced LSTM)
- **Automatic CSV Generation**: Creates prediction files directly after training completion

### Frontend
- **Custom Date Range Selection**: Flexible date picker with 1 day to 3 years range constraints
- **CSV-Based Data Loading**: Direct file reading for improved performance and reliability
- **Interactive Charts**: ECharts-powered visualization with smooth animations
- **Responsive Design**: Works seamlessly on desktop, tablet, and mobile devices
- **Dark Mode UI**: Bitcoin-themed styling with modern design
- **Real-time Filtering**: Instant data filtering based on selected date ranges

## Tech Stack

### AI/Backend
- Python 3.8+ (recommended: 3.11 or lower)
- TensorFlow 2.x for LSTM/GRU models
- NumPy, pandas for data manipulation
- scikit-learn for model evaluation
- joblib for model persistence
- matplotlib for visualization

### Frontend
- React with TypeScript
- ECharts for data visualization
- Styled Components for styling
- PapaParse for CSV parsing
- Custom date picker components

## Getting Started

### Prerequisites

1. Python 3.8+ (recommended: 3.11 or lower)
2. Node.js and npm
3. Git

### Installation

#### Clone the Repository
```bash
git clone <repository-url>
cd hackthon_hnt_team
```

#### AI Model Setup
1. Install Python dependencies:
```bash
pip install tensorflow numpy pandas scikit-learn joblib matplotlib plotly
```

2. Train the model and generate predictions:
```bash
cd AI
python bitcoin_price_prediction_using_lstm.py
```
This will:
- Train the enhanced LSTM/GRU ensemble model
- Generate 5-year daily predictions
- Save predictions to CSV files in both Data/ and Frontend/public/Data/ directories

#### Frontend Setup
1. Install Node.js dependencies:
```bash
cd Frontend
npm install
```

2. Start the development server:
```bash
npm start
```
The frontend will be available at http://localhost:3000

## Data Flow Architecture

The system now uses a CSV-based data flow for improved performance:

1. **Model Training** → Generates `bitcoin_predictions_5year.csv` with 5 years of daily predictions
2. **CSV Storage** → Saves to both `Data/` and `Frontend/public/Data/` directories
3. **Frontend Loading** → Directly reads CSV file using PapaParse
4. **Date Filtering** → Users select custom date ranges (1 day to 3 years)
5. **Chart Display** → ECharts renders filtered prediction data

## Usage Guide

### Training the Model
```bash
cd AI
python bitcoin_price_prediction_using_lstm.py
```

### Using the Frontend
1. **Start the application** - Run `npm start` in the Frontend directory
2. **Wait for CSV generation** - If no predictions appear, train the model first
3. **Select date range** - Use the date picker to choose your prediction timeframe
4. **View predictions** - Interactive chart displays the selected date range
5. **Customize range** - Minimum 1 day ahead, maximum 3 years ahead

### CSV File Format
The generated `bitcoin_predictions_5year.csv` contains:
```csv
Date,Predicted_Price
2024-01-01,67250.85
2024-01-02,67450.32
2024-01-03,67680.12
...
```

### Date Range Constraints
- **Minimum**: 1 day from today
- **Maximum**: 3 years from today
- **Selection limit**: Maximum 3-year range (even though CSV contains 5 years)
- **Validation**: Built-in date range validation with error messages

## Enhanced Model Architecture

The system uses an advanced ensemble approach:

### Model Components
- **Enhanced LSTM**: Multi-layer LSTM with 4 layers, 50 units each
- **Enhanced GRU**: Multi-layer GRU with 4 layers, 50 units each
- **Advanced LSTM**: Functional API model with 64 units and dense layers
- **Ensemble Prediction**: Combines multiple models for improved accuracy

### Training Features
- **Window Size**: 60-day lookback period
- **Multivariate Input**: Price + Volume data for enhanced predictions
- **Rolling Window Validation**: 5-fold time series validation
- **Early Stopping**: Prevents overfitting with patience-based stopping
- **Learning Rate Reduction**: Adaptive learning rate scheduling

## Model Evaluation

The model is evaluated using:
- Root Mean Squared Error (RMSE)
- Mean Absolute Error (MAE)
- R² Score (coefficient of determination)
- Visualization of model performance is saved in `AI/model/` directory

## CSV Data Integration Example

```typescript
// Example of loading CSV predictions
import { loadPredictionCSV, filterPredictionsByDateRange } from './utils/csvDataLoader';

async function loadPredictions() {
  try {
    // Load all prediction data from CSV
    const allPredictions = await loadPredictionCSV();

    // Filter by date range
    const dateRange = {
      startDate: new Date('2024-01-01'),
      endDate: new Date('2024-12-31')
    };

    const filteredData = filterPredictionsByDateRange(allPredictions, dateRange);

    // Use with your charting library
    console.log(`Loaded ${filteredData.dates.length} predictions`);
    console.log(`Price range: $${Math.min(...filteredData.prices)} - $${Math.max(...filteredData.prices)}`);

    return filteredData;
  } catch (error) {
    console.error('Error loading CSV predictions:', error);
  }
}
```

## Troubleshooting

### CSV Loading Issues
- **File not found**: Ensure the model has been trained and CSV file exists in `Frontend/public/Data/`
- **Empty predictions**: Check that the CSV file contains valid data with Date and Predicted_Price columns
- **Date range errors**: Verify selected dates are within 1 day to 3 years from today

### Model Training Issues
- **TensorFlow errors**: Ensure TensorFlow 2.x is properly installed and compatible with your system
- **Memory issues**: For large datasets, consider reducing batch size or using GPU acceleration
- **CUDA setup**: For GPU acceleration, ensure CUDA and cuDNN are properly configured
- **Data format**: Verify `Bitcoin Historical Data.csv` is in the correct format with required columns

### Frontend Issues
- **Chart not displaying**: Check browser console for CSV loading errors
- **Date picker not working**: Ensure date constraints are properly set (1 day to 3 years)
- **Performance issues**: Large date ranges may cause slower rendering; consider smaller ranges

## License

This project is for hackathon purposes only.

## Authors

HNT Team
